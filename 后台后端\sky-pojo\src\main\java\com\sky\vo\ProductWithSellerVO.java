package com.sky.vo;

import com.sky.entity.PmsProduct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品及其商家信息的视图对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductWithSellerVO {
    
    /**
     * 商品信息
     */
    private PmsProduct product;
    
    /**
     * 商家信息
     */
    private SellerInfoVO seller;
    
    /**
     * 商家信息视图对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SellerInfoVO {
        private Long id;
        private String accountName;
        private String phone;
        private String email;
        private Integer accountStatus;
        private String photoUrl;
    }
}
