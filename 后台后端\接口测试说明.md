# 商品审核接口测试说明

## 测试环境

- 后台服务地址：http://localhost:8888
- 接口路径：/products/admin

## 测试步骤

### 1. 启动后台服务

确保后台Spring Boot应用已启动并运行在8888端口。

### 2. 测试接口调用

#### 使用curl命令测试：

```bash
curl -X GET "http://localhost:8888/products/admin" \
  -H "Content-Type: application/json"
```

#### 使用Postman测试：

1. 创建新的GET请求
2. URL: `http://localhost:8888/products/admin`
3. Headers: `Content-Type: application/json`
4. 发送请求

#### 使用浏览器测试：

直接在浏览器中访问：`http://localhost:8888/products/admin`

### 3. 预期响应

成功响应应该包含以下结构：

```json
{
  "code": 1,
  "msg": "success", 
  "data": [
    {
      "product": {
        "id": 商品ID,
        "name": "商品名称",
        "price": 价格,
        "creatorUserId": 商家ID,
        // ... 其他商品字段
      },
      "seller": {
        "id": 商家ID,
        "accountName": "商家名称",
        "phone": "商家电话",
        "email": "商家邮箱",
        "accountStatus": 账户状态,
        "photoUrl": "头像URL"
      }
    }
  ]
}
```

### 4. 可能的问题及解决方案

#### 问题1：404 Not Found
- **原因：** 服务未启动或端口不正确
- **解决：** 检查服务状态，确认端口号

#### 问题2：500 Internal Server Error
- **原因：** 数据库连接问题或SQL语句错误
- **解决：** 检查数据库连接配置，查看服务日志

#### 问题3：返回空数据
- **原因：** 数据库中没有商品数据
- **解决：** 添加测试数据或检查数据库表

#### 问题4：商家信息为null
- **原因：** 商品的creator_user_id字段与seller表的id字段不匹配
- **解决：** 检查数据完整性，确保关联关系正确

## 数据库验证

可以直接查询数据库验证数据：

```sql
-- 查看商品及商家信息
SELECT 
    p.id as product_id,
    p.name as product_name,
    p.creator_user_id,
    s.id as seller_id,
    s.account_name as seller_name
FROM ry_mall.pms_product p
LEFT JOIN ry_mall.seller s ON p.creator_user_id = s.id
LIMIT 10;
```

## 前端集成测试

前端开发人员可以使用以下JavaScript代码测试接口：

```javascript
// 使用fetch API测试
fetch('http://localhost:8888/products/admin')
  .then(response => response.json())
  .then(data => {
    console.log('接口响应:', data);
    if (data.code === 1 && data.data) {
      data.data.forEach(item => {
        console.log('商品:', item.product.name);
        console.log('商家:', item.seller?.accountName || '未知商家');
      });
    }
  })
  .catch(error => {
    console.error('请求失败:', error);
  });
```

## 性能测试

对于大量数据的情况，建议进行性能测试：

1. 测试返回时间是否在可接受范围内（建议<2秒）
2. 测试内存使用情况
3. 测试并发访问能力

## 安全测试

1. 验证是否需要管理员权限
2. 测试SQL注入防护
3. 测试XSS防护
