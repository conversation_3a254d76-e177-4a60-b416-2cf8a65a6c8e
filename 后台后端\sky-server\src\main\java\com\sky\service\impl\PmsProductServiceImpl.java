package com.sky.service.impl;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.sky.entity.PmsProduct;
import com.sky.Utils.OssUtils;
import com.sky.entity.Variant;

import com.sky.exception.IdIsNullException;
import com.sky.mapper.PmsProductMapper;
import com.sky.mapper.VariantMapper;

import com.sky.service.PmsProductService;
import com.sky.vo.ProductWithSellerVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
public class PmsProductServiceImpl implements PmsProductService {

    @Autowired
    private PmsProductMapper pmsProductMapper;
    @Autowired
    private VariantMapper variantMapper;
    @Autowired
    private OssUtils ossUtils;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    @Override
    public List<PmsProduct> getProductsBySellerId(Long sellerId) {
        List<PmsProduct> products = pmsProductMapper.getProductsBySellerId(sellerId);
        for (PmsProduct product : products){
            String outProductId = product.getOutProductId();
            List<Variant> list = variantMapper.selectVariants(outProductId);
            product.setProductVariant(list);
        }
        return products;
    }

    @Override
    public List<PmsProduct> getAllProducts() {
        List<PmsProduct> allProducts = pmsProductMapper.getAllProducts();
        for (PmsProduct product : allProducts){
            String outProductId = product.getOutProductId();
            List<Variant> list = variantMapper.selectVariants(outProductId);
            product.setProductVariant(list);
        }
        return allProducts;
    }

    @Override
    public List<ProductWithSellerVO> getAllProductsWithSeller() {
        List<ProductWithSellerVO> productsWithSeller = pmsProductMapper.getAllProductsWithSeller();
        for (ProductWithSellerVO productWithSeller : productsWithSeller) {
            PmsProduct product = productWithSeller.getProduct();
            if (product != null && product.getOutProductId() != null) {
                String outProductId = product.getOutProductId();
                List<Variant> list = variantMapper.selectVariants(outProductId);
                product.setProductVariant(list);
            }
        }
        return productsWithSeller;
    }

    @Override
    @Transactional
    public PmsProduct addProduct(Long sellerId, PmsProduct product) {
        // 生成唯一的 8 位商品编码
        String productCode = generateUniqueProductCode();

        String[] variantPicList = {};
        //获得变体图片列表
        if (product.getVariantPics() != null && !product.getVariantPics().isEmpty()){
            variantPicList = product.getVariantPics().split(",");
        }

        List<Variant> productVariant = product.getProductVariant();
        if (productVariant == null && productVariant.size() <= 0) {
            throw new NullPointerException("变体列表为空");
        }

        for (int i = 0; i < productVariant.size(); i++) {
            Variant variant = productVariant.get(i);
            variant.setProductUPC(generateUniqueProductCode());
            variant.setOutProductId(productCode);
            if (variantPicList.length > 0) {
                productVariant.get(i).setVariantPic(variantPicList[i]);
            }
            variantMapper.insert(productVariant.get(i));
        }


        product.setOutProductId(productCode);
        product.setCreatorUserId(sellerId);
        product.setCreateTime(new Date());
        product.setUpdateTime(new Date());
        product.setStatus(0);
        if (product.getEnableVariant() == null){
            product.setEnableVariant(0);
        }

        // 设置默认值
        if (product.getPublishStatus() == null) {
            product.setPublishStatus(0);
        }
        if (product.getSort() == null) {
            product.setSort(0);
        }
        if (product.getPrice() == null) {
            product.setPrice(BigDecimal.ZERO);
        }
        if (product.getUnit() == null) {
            product.setUnit("件");
        }
        if (product.getWeight() == null) {
            product.setWeight(BigDecimal.ZERO);
        }

        // 处理图片上传
        pmsProductMapper.addProduct(product);

        return product;
    }

    @Override
    public void deleteProduct(Long sellerId, Long productId) {
        PmsProduct product = pmsProductMapper.getProductById(productId);
        String outProductId = product.getOutProductId();
        if (product != null && product.getCreatorUserId().equals(sellerId)) {
            pmsProductMapper.deleteProduct(productId);
            variantMapper.deleteVariant(outProductId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public PmsProduct updateProduct(Long sellerId, Long productId, PmsProduct product) {

        PmsProduct existingProduct = pmsProductMapper.getProductById(productId);
        if (existingProduct != null && existingProduct.getCreatorUserId().equals(sellerId)) {
            // 商品编码不能修改
            product.setOutProductId(existingProduct.getOutProductId());
            product.setId(productId);
            product.setCreatorUserId(sellerId);
            product.setUpdateTime(new Date());

            // 处理图片上传
            pmsProductMapper.updateProduct(product);

            //查询旧变体列表
            List<Variant> oldVariants = variantMapper.selectVariants(existingProduct.getOutProductId());

            product.getProductVariant().stream().forEach(variant -> {
                if (variant.getVariantId() != null){
                    Variant old = oldVariants.stream().filter(variant1 -> variant1.getVariantId().equals(variant.getVariantId())).findFirst().orElse(null);
                    if (old != null && variant.getVariantPic() == null){
                        variant.setVariantPic(old.getVariantPic());
                    }
                    variantMapper.update(variant);
                }else {
                    // 新变体：生成编码并插入（如果需要默认图片可在此设置）
                    variant.setProductUPC(generateUniqueProductCode());
                    variant.setOutProductId(existingProduct.getOutProductId());
                    variantMapper.insert(variant);
                }
            });

            //更新变体列表
            variantMapper.deleteVariant(existingProduct.getOutProductId());
            for (Variant variant : product.getProductVariant()) {
                variant.setProductUPC(generateUniqueProductCode());
                variant.setOutProductId(existingProduct.getOutProductId());
                variantMapper.insert(variant);
            }

            Long id = product.getId();
            stringRedisTemplate.delete("product:" + id);
            PmsProduct productById = pmsProductMapper.getProductById(productId);
            productById.setProductVariant(oldVariants);

            return productById;
        }
        return null;
    }

    @Override
    public PmsProduct getProductsById(Long productId) {
        PmsProduct productById = pmsProductMapper.getProductById(productId);

        String outProductId = productById.getOutProductId();
        List<Variant> variants = variantMapper.selectVariants(outProductId);


        String variantPics = new String();
        for (Variant variant : variants) {
            variantPics = variantPics + variant.getVariantPic() + ",";
        }

        productById.setVariantPics(variantPics);
        productById.setProductVariant(variants);
        return productById;
    }

    @Override
    public void deleteProductById(Long id) {
        PmsProduct product = pmsProductMapper.getProductById(id);
        String outProductId = product.getOutProductId();
        pmsProductMapper.deleteProduct(id);
        variantMapper.deleteVariant(outProductId);
    }

    @Override
    public void changePass(Long id) {
        PmsProduct productById = pmsProductMapper.getProductById(id);
        productById.setStatus(1);
        pmsProductMapper.updateProduct(productById);
    }


    private String generateUniqueProductCode() {
        Random random = new Random();
        String code;
        do {
            code = String.format("%08d", random.nextInt(100000000));
        } while (pmsProductMapper.isProductCodeExists(code) && variantMapper.isProductCodeExists(code));
        return code;
    }


}