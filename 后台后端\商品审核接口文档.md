# 商品审核接口文档

## 接口概述

本文档描述了后台管理系统中商品审核相关的接口，特别是 `/products/admin` 接口的改造，该接口现在返回商品信息及其对应的商家信息。

## 接口详情

### 1. 查询所有商品及商家信息

**接口地址：** `GET http://localhost:8888/products/admin`

**接口描述：** 超级管理员查询所有商品，返回商品信息及其对应的商家信息

**请求方式：** GET

**请求参数：** 无

**响应格式：** JSON

**响应数据结构：**

```json
{
  "code": 1,
  "msg": "success",
  "data": [
    {
      "product": {
        "id": 1,
        "productSnapshotId": null,
        "inventory": 100,
        "status": 1,
        "brandId": 1,
        "categoryId": 1,
        "creatorUserId": 1,
        "outProductId": "P12345678",
        "name": "商品名称",
        "pic": "https://example.com/product.jpg",
        "albumPics": "https://example.com/album1.jpg,https://example.com/album2.jpg",
        "publishStatus": 1,
        "sort": 1,
        "price": 99.99,
        "unit": "件",
        "weight": 500.00,
        "detailHtml": "<p>商品详情</p>",
        "detailMobileHtml": "<p>移动端商品详情</p>",
        "brandName": "品牌名称",
        "productCategoryName": "分类名称",
        "createBy": 1,
        "createTime": "2024-01-01T10:00:00",
        "updateBy": 1,
        "updateTime": "2024-01-01T10:00:00",
        "productAttr": "{}",
        "introductPics": "https://example.com/intro1.jpg",
        "pdfDocument": "https://example.com/document.pdf",
        "enableVariant": 1,
        "productVariant": [
          {
            "id": 1,
            "variantName": "规格1",
            "variantValue": "红色-L",
            "price": 99.99,
            "inventory": 50
          }
        ],
        "packageInfo": {
          "length": 10.0,
          "width": 8.0,
          "height": 5.0,
          "weight": 0.5
        },
        "variantPics": "https://example.com/variant1.jpg"
      },
      "seller": {
        "id": 1,
        "accountName": "商家账户名",
        "phone": "***********",
        "email": "<EMAIL>",
        "accountStatus": 1,
        "photoUrl": "https://example.com/seller-avatar.jpg"
      }
    }
  ]
}
```

**字段说明：**

#### Product 字段说明：
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 商品ID |
| productSnapshotId | Long | 商品快照ID |
| inventory | Long | 库存数量 |
| status | Integer | 商品状态 |
| brandId | Long | 品牌ID |
| categoryId | Long | 分类ID |
| creatorUserId | Long | 创建者用户ID（商家ID） |
| outProductId | String | 商品编码 |
| name | String | 商品名称 |
| pic | String | 主图URL |
| albumPics | String | 画册图片URL（逗号分隔） |
| publishStatus | Integer | 上架状态：0->下架；1->上架 |
| sort | Integer | 排序 |
| price | BigDecimal | 价格 |
| unit | String | 单位 |
| weight | BigDecimal | 商品重量（克） |
| detailHtml | String | 产品详情网页内容 |
| detailMobileHtml | String | 移动端网页详情 |
| brandName | String | 品牌名称 |
| productCategoryName | String | 商品分类名称 |
| createBy | Long | 创建人 |
| createTime | Date | 创建时间 |
| updateBy | Long | 修改人 |
| updateTime | Date | 修改时间 |
| productAttr | String | 商品销售属性（JSON格式） |
| introductPics | String | 图文介绍列表 |
| pdfDocument | String | 商品介绍PDF文档 |
| enableVariant | Integer | 是否启用变体：1->启用；0->不启用 |
| productVariant | List | 商品变体列表 |
| packageInfo | Object | 商品包装信息 |
| variantPics | String | 变体图片列表 |

#### Seller 字段说明：
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 商家ID |
| accountName | String | 商家账户名 |
| phone | String | 商家电话 |
| email | String | 商家邮箱 |
| accountStatus | Integer | 账户状态：1->启用；0->停用 |
| photoUrl | String | 商家头像URL |

**响应状态码：**
- 200：请求成功
- 500：服务器内部错误

## 接口变更说明

### 变更内容：
1. **原接口返回：** 仅返回商品信息列表
2. **新接口返回：** 返回商品信息及其对应的商家信息

### 数据库关联：
- 商品表（pms_product）通过 `creator_user_id` 字段关联商家表（seller）的 `id` 字段
- 使用 LEFT JOIN 查询，确保即使商家信息缺失也能返回商品信息

### 前端适配建议：

1. **数据结构调整：** 
   - 原来直接访问商品属性：`item.name`
   - 现在需要通过product对象访问：`item.product.name`
   - 商家信息通过seller对象访问：`item.seller.accountName`

2. **显示商家信息：**
   ```javascript
   // 商家名称
   const sellerName = item.seller?.accountName || '未知商家';
   
   // 商家联系方式
   const sellerPhone = item.seller?.phone || '';
   const sellerEmail = item.seller?.email || '';
   
   // 商家状态
   const sellerStatus = item.seller?.accountStatus === 1 ? '正常' : '停用';
   ```

3. **表格列配置示例：**
   ```javascript
   const columns = [
     { title: '商品名称', dataIndex: ['product', 'name'] },
     { title: '商品价格', dataIndex: ['product', 'price'] },
     { title: '商家名称', dataIndex: ['seller', 'accountName'] },
     { title: '商家电话', dataIndex: ['seller', 'phone'] },
     { title: '商家邮箱', dataIndex: ['seller', 'email'] },
     { title: '商家状态', dataIndex: ['seller', 'accountStatus'], 
       render: (status) => status === 1 ? '正常' : '停用' }
   ];
   ```

## 相关接口

### 商品审核操作接口：

1. **审核通过：** `PUT /products/admin/changePass/{id}`
2. **审核拒绝：** `PUT /products/admin/changeDelete/{id}`
3. **查看单个商品：** `GET /products/{id}`

## 注意事项

1. 该接口需要超级管理员权限
2. 返回的商品列表按创建时间倒序排列
3. 商家信息可能为空（如果商家已被删除），前端需要做空值处理
4. 商品变体信息会自动加载并包含在响应中
