package com.sky.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sky.entity.PmsProduct;
import com.sky.vo.ProductWithSellerVO;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface PmsProductMapper extends BaseMapper<PmsProduct> {
    List<PmsProduct> getProductsBySellerId(Long sellerId);
    List<PmsProduct> getAllProducts();
    List<ProductWithSellerVO> getAllProductsWithSeller();
    void addProduct(PmsProduct product);
    void deleteProduct(Long productId);
    void updateProduct(PmsProduct product);
    PmsProduct getProductById(Long productId);
    boolean isProductCodeExists(String productCode);
}