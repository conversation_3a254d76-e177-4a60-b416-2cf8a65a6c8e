<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sky.mapper.PmsProductMapper">

    <resultMap id="PmsProductMap" type="com.sky.entity.PmsProduct">
        <id property="outProductId" column="out_product_id"/>
        <id property="brandId" column="brand_id"/>
        <id property="categoryId" column="category_id"/>
        <id property="creatorUserId" column="creator_user_id"/>
        <id property="name" column="name"/>
        <id property="pic" column="pic"/>
        <id property="albumPics" column="album_pics"/>
        <id property="publishStatus" column="publish_status"/>
        <id property="sort" column="sort"/>
        <id property="price" column="price"/>
        <id property="unit" column="unit"/>
        <id property="weight" column="weight"/>
        <id property="detailHtml" column="detail_html"/>
        <id property="detailMobileHtml" column="detail_mobile_html"/>
        <id property="brandName" column="brand_name"/>
        <id property="productCategoryName" column="product_category_name"/>
        <id property="createTime" column="create_time"/>
        <id property="updateTime" column="update_time"/>
        <id property="productAttr" column="product_attr"/>
        <id property="status" column="status"/>
        <id property="enableVariant" column="enable_variant"/>
        <id property="introductPics" column="introduct_pics"/>
        <id property="pdfDocument" column="pdf_document"/>
        <id property="packageInfo" column="package_info" typeHandler="com.sky.handler.PackageInfoTypeHandler"/>
    </resultMap>

    <select id="getProductsBySellerId" resultMap="PmsProductMap">
        SELECT * FROM ry_mall.pms_product WHERE creator_user_id = #{sellerId}
    </select>

    <select id="getAllProducts" resultMap="PmsProductMap">
        SELECT * FROM ry_mall.pms_product
    </select>

    <resultMap id="ProductWithSellerMap" type="com.sky.vo.ProductWithSellerVO">
        <association property="product" javaType="com.sky.entity.PmsProduct">
            <id property="id" column="product_id"/>
            <result property="outProductId" column="out_product_id"/>
            <result property="brandId" column="brand_id"/>
            <result property="categoryId" column="category_id"/>
            <result property="creatorUserId" column="creator_user_id"/>
            <result property="name" column="name"/>
            <result property="pic" column="pic"/>
            <result property="albumPics" column="album_pics"/>
            <result property="publishStatus" column="publish_status"/>
            <result property="sort" column="sort"/>
            <result property="price" column="price"/>
            <result property="unit" column="unit"/>
            <result property="weight" column="weight"/>
            <result property="detailHtml" column="detail_html"/>
            <result property="detailMobileHtml" column="detail_mobile_html"/>
            <result property="brandName" column="brand_name"/>
            <result property="productCategoryName" column="product_category_name"/>
            <result property="createTime" column="create_time"/>
            <result property="updateTime" column="update_time"/>
            <result property="productAttr" column="product_attr"/>
            <result property="status" column="status"/>
            <result property="enableVariant" column="enable_variant"/>
            <result property="introductPics" column="introduct_pics"/>
            <result property="pdfDocument" column="pdf_document"/>
            <result property="packageInfo" column="package_info" typeHandler="com.sky.handler.PackageInfoTypeHandler"/>
            <result property="inventory" column="inventory"/>
            <result property="productSnapshotId" column="product_snapshot_id"/>
            <result property="createBy" column="create_by"/>
            <result property="updateBy" column="update_by"/>
            <result property="variantPics" column="variant_pics"/>
        </association>
        <association property="seller" javaType="com.sky.vo.ProductWithSellerVO$SellerInfoVO">
            <id property="id" column="seller_id"/>
            <result property="accountName" column="seller_account_name"/>
            <result property="phone" column="seller_phone"/>
            <result property="email" column="seller_email"/>
            <result property="accountStatus" column="seller_account_status"/>
            <result property="photoUrl" column="seller_photo_url"/>
        </association>
    </resultMap>

    <select id="getAllProductsWithSeller" resultMap="ProductWithSellerMap">
        SELECT
            p.id as product_id,
            p.out_product_id,
            p.brand_id,
            p.category_id,
            p.creator_user_id,
            p.name,
            p.pic,
            p.album_pics,
            p.publish_status,
            p.sort,
            p.price,
            p.unit,
            p.weight,
            p.detail_html,
            p.detail_mobile_html,
            p.brand_name,
            p.product_category_name,
            p.create_time,
            p.update_time,
            p.product_attr,
            p.status,
            p.enable_variant,
            p.introduct_pics,
            p.pdf_document,
            p.package_info,
            p.inventory,
            p.product_snapshot_id,
            p.create_by,
            p.update_by,
            p.variant_pics,
            s.id as seller_id,
            s.account_name as seller_account_name,
            s.phone as seller_phone,
            s.email as seller_email,
            s.account_status as seller_account_status,
            s.photo_url as seller_photo_url
        FROM ry_mall.pms_product p
        LEFT JOIN ry_mall.seller s ON p.creator_user_id = s.id
        ORDER BY p.create_time DESC
    </select>

    <insert id="addProduct" parameterType="com.sky.entity.PmsProduct">
        INSERT INTO ry_mall.pms_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="outProductId != null">out_product_id,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="creatorUserId != null">creator_user_id,</if>
            <if test="name != null">name,</if>
            <if test="pic != null">pic,</if>
            <if test="albumPics != null">album_pics,</if>
            <if test="publishStatus != null">publish_status,</if>
            <if test="sort != null">sort,</if>
            <if test="price != null">price,</if>
            <if test="unit != null">unit,</if>
            <if test="weight != null">weight,</if>
            <if test="detailHtml != null">detail_html,</if>
            <if test="detailMobileHtml != null">detail_mobile_html,</if>
            <if test="brandName != null">brand_name,</if>
            <if test="productCategoryName != null">product_category_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="productAttr != null">product_attr,</if>
            <if test="introductPics != null">introduct_pics,</if>
            <if test="pdfDocument != null">pdf_document,</if>
            <if test="status != null">status,</if>
            <if test="enableVariant != null">enable_variant,</if>
            <if test="packageInfo != null">package_info</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="outProductId != null">#{outProductId},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="creatorUserId != null">#{creatorUserId},</if>
            <if test="name != null">#{name},</if>
            <if test="pic != null">#{pic},</if>
            <if test="albumPics != null">#{albumPics},</if>
            <if test="publishStatus != null">#{publishStatus},</if>
            <if test="sort != null">#{sort},</if>
            <if test="price != null">#{price},</if>
            <if test="unit != null">#{unit},</if>
            <if test="weight != null">#{weight},</if>
            <if test="detailHtml != null">#{detailHtml},</if>
            <if test="detailMobileHtml != null">#{detailMobileHtml},</if>
            <if test="brandName != null">#{brandName},</if>
            <if test="productCategoryName != null">#{productCategoryName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="productAttr != null">#{productAttr},</if>
            <if test="introductPics !=null">#{introductPics},</if>
            <if test="pdfDocument != null">#{pdfDocument},</if>
            <if test="status != null">#{status},</if>
            <if test="enableVariant != null">#{enableVariant},</if>
            <if test="packageInfo != null">#{packageInfo, typeHandler=com.sky.handler.PackageInfoTypeHandler}</if>
        </trim>
    </insert>

    <delete id="deleteProduct" parameterType="java.lang.Long">
        DELETE FROM ry_mall.pms_product WHERE id = #{productId}
    </delete>

    <update id="updateProduct" parameterType="com.sky.entity.PmsProduct">
        UPDATE ry_mall.pms_product
        <set>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="price != null">price = #{price},</if>
            <if test="status != null">status = #{status},</if>
            <if test="publishStatus != null">publish_status = #{publishStatus},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="pic != null">pic = #{pic},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="creatorUserId != null">creator_user_id = #{creatorUserId},</if>
            <if test="outProductId != null">out_product_id = #{outProductId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="albumPics != null">album_pics = #{albumPics},</if>
            <if test="detailHtml != null">detail_html = #{detailHtml},</if>
            <if test="inventory != null">inventory = #{inventory},</if>
            <if test="detailMobileHtml != null">detail_mobile_html = #{detailMobileHtml},</if>
            <if test="brandName != null">brand_name = #{brandName},</if>
            <if test="productCategoryName != null">product_category_name = #{productCategoryName},</if>
            <if test="productAttr != null">product_attr=#{productAttr},</if>
            <if test="introductPics != null">introduct_pics = #{introductPics},</if>
            <if test="pdfDocument != null">pdf_document = #{pdfDocument},</if>
            <if test="enableVariant != null">enable_variant = #{enableVariant},</if>
            <if test="packageInfo != null">package_info = #{packageInfo, typeHandler=com.sky.handler.PackageInfoTypeHandler}</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="getProductById" resultMap="PmsProductMap">
        SELECT * FROM ry_mall.pms_product WHERE id = #{productId}
    </select>

    <select id="isProductCodeExists" parameterType="java.lang.String" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM ry_mall.pms_product WHERE out_product_id = #{productCode}
    </select>

</mapper>