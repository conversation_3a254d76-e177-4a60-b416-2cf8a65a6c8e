package com.sky.controller.Seller;

import com.sky.Utils.OssUtils;

import com.sky.entity.PmsProduct;
import com.sky.result.Result;
import com.sky.service.PmsProductService;
import com.sky.vo.ProductWithSellerVO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/products")
public class PmsProductController {

    @Autowired
    private OssUtils ossUtils;

    @Autowired
    private PmsProductService pmsProductService;

    // 卖家查询自己的产品
    @GetMapping("/product/seller/{sellerId}")
    public Result<List<PmsProduct>> getProductsBySellerId(@PathVariable Long sellerId) {
        return Result.success(pmsProductService.getProductsBySellerId(sellerId));
    }

    // 卖家查询单个产品
    @GetMapping("/product/seller/{sellerId}/{productId}")
    public Result<PmsProduct> getProductsById(@PathVariable Long productId) {
        return Result.success(pmsProductService.getProductsById(productId));
    }

    // 超级管理员查询所有产品
    @GetMapping("/admin")
    public Result<List<ProductWithSellerVO>> getAllProducts() {
        return Result.success(pmsProductService.getAllProductsWithSeller());
    }

    // 卖家添加产品
    @PostMapping("/seller/{sellerId}")
    public Result<PmsProduct> addProduct(@PathVariable Long sellerId, @RequestPart PmsProduct pmsProduct,
                                                  @RequestPart(value = "pic",required = false) MultipartFile pic,
                                                  @RequestPart(value = "albumPics",required = false) MultipartFile[] albumPics,
                                                  @RequestPart(value = "introductPics",required = false) MultipartFile[] introductPics,
                                                  @RequestPart(value = "pdfDocument",required = false) MultipartFile[] pdfDocument,
                                                  @RequestPart(value = "variantPics",required = false) MultipartFile[] variantPics) {
        if(pic != null) {
            String s = ossUtils.uploadOneFile(pic);
            pmsProduct.setPic(s);
        }
        if(albumPics != null && albumPics.length > 0){
            List<String> strings = ossUtils.uploadArrayFile(albumPics);
            String Url = String.join(",", strings);
            pmsProduct.setAlbumPics(Url);
        }

        if (introductPics != null && introductPics.length > 0){
            List<String> strings = ossUtils.uploadArrayFile(introductPics);
            String Url = String.join(",", strings);
            pmsProduct.setIntroductPics(Url);
        }

        if (pdfDocument != null && pdfDocument.length > 0){
            List<String> strings = ossUtils.uploadArrayFile(pdfDocument);
            String Url = String.join(",", strings);
            pmsProduct.setPdfDocument(Url);
        }

        if (variantPics != null && variantPics.length > 0){
            List<String> strings = ossUtils.uploadArrayFile(variantPics);
            String Url = String.join(",", strings);
            pmsProduct.setVariantPics(Url);
        }

        return Result.success(pmsProductService.addProduct(sellerId, pmsProduct));

    }

    // 卖家删除自己的产品
    @DeleteMapping("/seller/{sellerId}/{productId}")
    public Result deleteProduct(@PathVariable Long sellerId, @PathVariable Long productId) {
        pmsProductService.deleteProduct(sellerId, productId);
        return Result.success("删除成功");
    }

    // 卖家修改自己的产品
    @PutMapping("/seller/{sellerId}/{productId}")
    @Transactional(rollbackFor = Exception.class)
    public Result<PmsProduct> updateProduct(@PathVariable Long sellerId, @PathVariable Long productId,
                                            @RequestPart PmsProduct product,
                                            @RequestPart(value = "pic",required = false) MultipartFile pic,
                                            @RequestPart(value = "albumPics",required = false) MultipartFile[] albumPics,
                                            @RequestPart(value = "introductPics",required = false) MultipartFile[] introductPics,
                                            @RequestPart(value = "pdfDocument",required = false) MultipartFile[] pdfDocument,
                                            @RequestPart(value = "variantPics",required = false) MultipartFile[] variantPics) {
        if(pic != null) {
            String s = ossUtils.uploadOneFile(pic);
            product.setPic(s);
        }
        if(albumPics != null && albumPics.length > 0){
            List<String> strings = ossUtils.uploadArrayFile(albumPics);
            String Url = String.join(",", strings);
            product.setAlbumPics(Url);
        }
        if (introductPics != null && introductPics.length > 0){
            List<String> strings = ossUtils.uploadArrayFile(introductPics);
            String Url = String.join(",", strings);
            product.setIntroductPics(Url);
        }
        if (pdfDocument != null && pdfDocument.length > 0){
            List<String> strings = ossUtils.uploadArrayFile(pdfDocument);
            String Url = String.join(",", strings);
            product.setIntroductPics(Url);
        }
        if (variantPics != null && variantPics.length > 0){
            List<String> strings = ossUtils.uploadArrayFile(variantPics);
            String Url = String.join(",", strings);
            product.setVariantPics(Url);
        }
        return Result.success(pmsProductService.updateProduct(sellerId, productId, product));
    }

    /**
     * 传入商品的id，审核通过商品
     * @param id
     * @return
     */
    @PutMapping("/admin/changePass/{id}")
    public Result changePass(@PathVariable Long id) {
        pmsProductService.changePass(id);
        return Result.success();
    }
    /**
     * 传入商品的id,审核不通过及删除商品
     * @param id
     * @return
     */
    @PutMapping("/admin/changeDelete/{id}")
    public Result changeDelete(@PathVariable Long id) {
        pmsProductService.deleteProductById(id);
        return Result.success();
    }

    /**
     * 查看单个商品详情
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public Result<PmsProduct> getProductById(@PathVariable Long id) {
        PmsProduct productsById = pmsProductService.getProductsById(id);
        return Result.success(productsById);
    }


}