<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.ProductMapper">

    <resultMap id="PmsProductMap" type="com.sky.entity.PmsProduct">
        <id property="outProductId" column="out_product_id"/>
        <id property="brandId" column="brand_id"/>
        <id property="categoryId" column="category_id"/>
        <id property="creatorUserId" column="creator_user_id"/>
        <id property="name" column="name"/>
        <id property="pic" column="pic"/>
        <id property="albumPics" column="album_pics"/>
        <id property="publishStatus" column="publish_status"/>
        <id property="sort" column="sort"/>
        <id property="price" column="price"/>
        <id property="unit" column="unit"/>
        <id property="weight" column="weight"/>
        <id property="detailHtml" column="detail_html"/>
        <id property="detailMobileHtml" column="detail_mobile_html"/>
        <id property="brandName" column="brand_name"/>
        <id property="productCategoryName" column="product_category_name"/>
        <id property="createTime" column="create_time"/>
        <id property="updateTime" column="update_time"/>
        <id property="productAttr" column="product_attr"/>
        <id property="status" column="status"/>
        <id property="enableVariant" column="enable_variant"/>
        <id property="packageInfo" column="package_info" typeHandler="com.sky.handler.PackageInfoTypeHandler"/>
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into product (product_number,store_id, product_name, price, product_introduction, product_image, product_video, product_status, create_time, type)
        values (#{productNumber}, #{storeId}, #{productName}, #{price}, #{productIntroduction}, #{productImage}, #{productVideo}, #{productStatus},
                #{createTime}, #{type})
    </insert>

    <select id="pageQuery" resultType="com.sky.vo.ProductVO">
        select d.* , c.name as categoryName from product d left outer join category c on d.category_id = c.id
        <where>
            <if test="productName != null">
                and d.product_name like concat('%',#{productName},'%')
            </if>
            <if test="categoryId != null">
                and d.category_id = #{categoryId}
            </if>
            <if test="productStatus != null">
                and d.product_status = #{productStatus}
            </if>
        </where>
        order by d.create_time desc
    </select>

    <select id="getById" resultMap="PmsProductMap">
        select * from pms_product where id = #{id}
    </select>

    <select id="getByCategoryId" resultMap="PmsProductMap">
        select * from pms_product where category_id = #{categoryId}
    </select>
    <select id="getByCategoryId1" resultMap="PmsProductMap">
        select * from ry_mall.pms_product
    </select>

    <update id="update">
        update product
    <set>
        <if test="productNumber != null">product_number = #{productNumber},</if>
        <if test="storeId != null">store_id = #{storeId},</if>
        <if test="productName != null">product_name = #{productName},</if>
        <if test="price != null">price = #{price},</if>
        <if test="productIntroduction != null">product_introduction = #{productIntroduction},</if>
        <if test="productImage != null">product_image = #{productImage},</if>
        <if test="productVideo != null">product_video = #{productVideo},</if>
        <if test="productStatus != null">product_status = #{productStatus},</if>
        <if test="createTime != null">create_time = #{createTime},</if>
        <if test="updateTime != null">update_time = #{updateTime},</if>
        <if test="type != null">type = #{type},</if>
        <if test="categoryId != null">category_id = #{categoryId},</if>
    </set>
    where id = #{id}
    </update>
</mapper>
