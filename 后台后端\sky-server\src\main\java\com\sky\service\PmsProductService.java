package com.sky.service;


import com.sky.entity.PmsProduct;
import com.sky.vo.ProductWithSellerVO;

import java.util.List;

public interface PmsProductService {
    List<PmsProduct> getProductsBySellerId(Long sellerId);
    List<PmsProduct> getAllProducts();
    List<ProductWithSellerVO> getAllProductsWithSeller();
    PmsProduct addProduct(Long sellerId, PmsProduct pmsProduct);
    void deleteProduct(Long sellerId, Long productId);
    PmsProduct updateProduct(Long sellerId, Long productId, PmsProduct product);

    PmsProduct getProductsById(Long productId);

    void changePass(Long id);

    void deleteProductById(Long id);
}