server:
  address: 0.0.0.0  # 允许所有IP访问
  port: 8443
  ssl:
    enabled: true
    key-store: classpath:ssl.p12
    key-store-password: chr123456 # 密钥库密码
    key-store-type: PKCS12
    key-password: chr123456  # 如果密钥文件有独立的密码，确保它也匹配
    key-alias: hiram
  tomcat:
    threads:
      min-spare: 20    # 最小空闲线程
      max: 200         # 最大线程数
    accept-count: 100  # 队列容量
    max-connections: 10000

spring:
  servlet:
    multipart:
      enabled: true              # 是否启用文件上传支持（默认 true）
      max-file-size: 10MB        # 单个文件的最大大小
      max-request-size: 50MB    # 整个请求的最大大小
      file-size-threshold: 1MB   # 文件写入磁盘的阈值
      location: /tmp             # 临时文件存储路径
  mvc:
    static-path-pattern: /**
  web:
    resources:
      static-locations: classpath:/static/
  profiles:
    active: dev
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  datasource:
    druid:
      # 连接池配置
      initial-size: 1                # 初始连接数，建议与最小空闲连接数一致
      max-active: 20                 # 最大连接池数量
      min-idle: 1                    # 最小空闲连接数

      # 连接等待配置
      max-wait: 3000                 # 获取连接最大等待时间(毫秒)，超过则报错

      # 连接验证配置
      validation-query: SELECT 1     # 用于验证连接有效性的SQL，简单高效
      test-on-borrow: true           # 获取连接时验证有效性
      test-while-idle: true          # 空闲时验证连接有效性

      # 失败重试配置
      time-between-connect-error-millis: 10000  # 连接出错后的重试间隔(毫秒)

      # 连接回收配置
      time-between-eviction-runs-millis: 60000  # 检查空闲连接的间隔(毫秒)
      min-evictable-idle-time-millis: 300000    # 连接最小空闲时间(毫秒)

      driver-class-name: ${sky.datasource.driver-class-name}
      url: jdbc:mysql://${sky.datasource.host}:${sky.datasource.port}/${sky.datasource.database}?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true
      username: ${sky.datasource.username}
      password: ${sky.datasource.password}
  redis:
    host: ${sky.redis.host}
    port: ${sky.redis.port}
    database: ${sky.redis.database}
  config:
    import: "classpath:/kaptcha.properties"
  #mapper配置文件
mybatis:
  #mapper配置文件
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.sky.entity
  configuration:
    #开启驼峰命名
    map-underscore-to-camel-case: true
mybatis-plus:
  type-aliases-package: com.sky.entity
  mapper-locations: classpath:mapper/*.xml
  global-config:
    db-config:
      id-type: auto
      update-strategy: not_null
      column-format: "`%s`" # 自动给列名加反引号

logging:
  level:
    com:
      sky:
        mapper: debug
        service: info
        controller: info
  org.apache.ibatis: DEBUG

sky:
  jwt:
    # 设置jwt签名加密时使用的秘钥
    admin-secret-key: itcast
    # 设置jwt过期时间
    admin-ttl: 7200000
    # 设置前端传递过来的令牌名称
    admin-token-name: token
    user-secret-key: itheima
    user-ttl: 7200000
    user-token-name: authentication
  alioss:
    endpoint: oss-cn-beijing.aliyuncs.com
    access-key-id: LTAI5t6EkRTHhvZwYs2S2VjC
    access-key-secret: ******************************
    bucket-name: sky-take-out
  wechat:
    appid: wx2bd197bcb4b986a0
    secret: 5a13920e83eecc8b6e61e9d4a1183b46
    mchid: 777463044
    # 使用正确的商户证书序列号
    mch-serial-no: 7B90DEE3ABB5B5BCF3048BC24F72C0A767BCE451
    private-key-path: cert/apiclient_key.pem
    api-v3-key: abcdefghigklmnopqrstuvwxyzabzdef
    notify-url: https://www.sharewharf.com/pay/notify
    refund-notify-url: https://www.sharewharf.com/pay/refund/notify

# 微信支付相关配置
wechat:
  pay:
    # 商户号
    mchid: 777463044
    # 商户API证书序列号 - 统一使用正确的序列号
    mchSerialNo: 7B90DEE3ABB5B5BCF3048BC24F72C0A767BCE451
    # 商户私钥文件路径
    privateKeyPath: apiclient_key.pem
    # APIv3密钥
    apiV3Key: abcdefghigklmnopqrstuvwxyzabzdef
    # APPID
    appid: wx2bd197bcb4b986a0
    # 微信支付回调通知地址 - 使用正确的域名
    notifyUrl: https://www.sharewharf.com/pay/notify
    # 跨境支付相关配置
    crossBorder:
      # 是否启用跨境支付
      enabled: true
      # 商户分类代码
      merchantCategoryCode: 4111
      # 交易类型
      tradeType: NATIVE
      # 货币类型
      currency: USD
      # 查询订单API基础URL
      queryApiBaseUrl: https://apihk.mch.weixin.qq.com/v3/global/transactions

# 17TRACK API配置
track17:
  api:
    key: ${TRACK17_API_KEY:your-17track-api-key-here}
    url: https://api.17track.net/track/v2.2
